"""
Celery Worker应用配置 - 参考remote_celery_worker项目
用于消费Redis队列中的任务并执行实际的业务逻辑
"""
from datetime import timedelta

from celery import Celery

from db.mysql import UsingMysql
from settings.config import settings

# 获取Redis连接信息（使用老项目的方法）
with UsingMysql() as um:
    redis_url = um.fetch_decrypt("select value from info_db where index_information = 'redis' ",
                                 None, 'value')

# 创建Celery应用实例
app = Celery('celery_task')

# 配置 - 使用老项目的Redis连接
config = {
    'broker_url': f"{redis_url}/{settings.celery.broker_db}",
    'result_backend': f"{redis_url}/{settings.celery.result_backend_db}",

    # 序列化配置
    'task_serializer': settings.celery.task_serializer,
    'result_serializer': settings.celery.result_serializer,
    'accept_content': settings.celery.accept_content,

    # 时区配置
    'timezone': settings.celery.timezone,
    'enable_utc': settings.celery.enable_utc,

    # 性能优化配置
    'result_compression': 'gzip',
    'worker_prefetch_multiplier': settings.celery.worker_prefetch_multiplier,
    'task_acks_late': settings.celery.task_acks_late,
    'worker_max_tasks_per_child': settings.celery.worker_max_tasks_per_child,
    'worker_max_memory_per_child': settings.celery.worker_max_memory_per_child,
    'task_queue_max_priority': settings.celery.task_queue_max_priority,
    'task_default_priority': settings.celery.task_default_priority,

    # 错误处理配置
    'task_acks_on_failure_or_timeout': settings.celery.task_acks_on_failure_or_timeout,
    'task_reject_on_worker_lost': settings.celery.task_reject_on_worker_lost,
    'result_backend_always_retry': settings.celery.result_backend_always_retry,
    'result_backend_max_retries': settings.celery.result_backend_max_retries,
    'result_backend_thread_safe': settings.celery.result_backend_thread_safe,
    'result_expires': timedelta(seconds=settings.celery.result_expires),

    # 连接重试配置
    'broker_connection_retry_on_startup': True,

    # 包含任务模块
    'include': [
        'tasks.proxy',
        'tasks.user_tasks',
        'tasks.role_tasks',
        'tasks.permission_tasks',
    ],
}

app.config_from_object(config)
