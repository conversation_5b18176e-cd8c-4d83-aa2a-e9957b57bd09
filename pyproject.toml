[project]
name = "chronic-disease-backend"
version = "0.1.0"
description = "慢病癌防项目后端真正处理业务逻辑的部分"
requires-python = ">=3.13"
dependencies = [
    "bcrypt>=4.3.0",
    "celery>=5.5.3",
    "dbutils>=3.1.1",
    "oracledb>=3.3.0",
    "pandas>=2.3.1",
    "pycryptodome>=3.23.0",
    "pydantic>=2.11.7",
    "pymysql>=1.1.1",
    "pyyaml>=6.0.2",
    "redis>=6.4.0",
    "regex>=2025.7.34",
    "sqlalchemy>=2.0.42",
]

[dependency-groups]
dev = [
    "flower>=2.0.1",
]
