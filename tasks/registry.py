"""
操作注册器 - 使用装饰器自动注册业务操作
支持实例方法的自动绑定
"""
import logging
import inspect
import weakref
from functools import wraps
from typing import Dict, Callable, Any

logger = logging.getLogger(__name__)


class OperationRegistry:
    """操作注册器"""

    def __init__(self):
        self._operations: Dict[str, Callable] = {}
        self._instance_methods: Dict[str, tuple] = {}  # 存储实例方法信息
        self._instances: weakref.WeakSet = weakref.WeakSet()  # 存储实例的弱引用

    def register(self, operation_name: str):
        """
        注册操作的装饰器
        支持实例方法和静态方法，自动绑定实例方法

        Args:
            operation_name: 操作名称，如 'user.login'
        """

        def decorator(func: Callable):
            # 检查是否是实例方法（第一个参数是self）
            sig = inspect.signature(func)
            params = list(sig.parameters.keys())
            is_instance_method = len(params) > 0 and params[0] == 'self'

            if is_instance_method:
                # 对于实例方法，创建一个智能wrapper
                @wraps(func)
                def instance_wrapper(*args, **kwargs):
                    # 尝试找到对应的实例
                    instance = self._find_instance_for_method(func)
                    if instance:
                        return func(instance, *args, **kwargs)
                    else:
                        raise RuntimeError(f"无法找到方法 {func.__name__} 的实例，请确保已创建相应的业务类实例")

                # 存储实例方法信息
                self._instance_methods[operation_name] = (func, None)
                # 注册wrapper
                self._operations[operation_name] = instance_wrapper
                logger.info(f"注册实例方法: {operation_name} -> {func.__module__}.{func.__name__}")

                # 返回一个特殊的装饰后方法，当它被调用时会自动注册实例
                @wraps(func)
                def decorated_method(instance_self, *args, **kwargs):
                    # 当方法第一次被调用时，注册这个实例
                    if operation_name in self._instance_methods:
                        current_func, current_instance = self._instance_methods[operation_name]
                        if current_instance is None:
                            # 注册实例
                            self._instance_methods[operation_name] = (current_func, instance_self)
                            self._instances.add(instance_self)
                            logger.info(f"自动绑定实例: {operation_name} -> {instance_self.__class__.__name__}")

                    return func(instance_self, *args, **kwargs)

                return decorated_method
            else:
                # 对于静态方法或函数，直接注册
                @wraps(func)
                def wrapper(*args, **kwargs):
                    return func(*args, **kwargs)

                self._operations[operation_name] = wrapper
                logger.info(f"注册操作: {operation_name} -> {func.__module__}.{func.__name__}")
                return wrapper

        return decorator

    def _find_instance_for_method(self, func: Callable) -> Any:
        """
        查找方法对应的实例

        Args:
            func: 方法函数

        Returns:
            对应的实例，如果找不到返回None
        """
        # 遍历所有已绑定的实例方法
        for operation_name, (method_func, instance) in self._instance_methods.items():
            if method_func == func and instance is not None:
                return instance

        # 如果没有找到绑定的实例，尝试从已知实例中查找
        for instance in self._instances:
            if hasattr(instance, func.__name__):
                method = getattr(instance, func.__name__)
                if hasattr(method, '__func__') and method.__func__ == func:
                    return instance

        return None

    def register_instance(self, instance: Any):
        """
        注册实例，自动绑定该实例的所有装饰过的方法

        Args:
            instance: 实例对象
        """
        instance_class = type(instance)
        for operation_name, (func, bound_instance) in list(self._instance_methods.items()):
            # 检查方法是否属于这个实例的类，且尚未绑定
            if hasattr(instance_class, func.__name__) and bound_instance is None:
                # 更新实例方法记录
                self._instance_methods[operation_name] = (func, instance)
                logger.info(f"绑定实例: {operation_name} -> {instance.__class__.__name__}.{func.__name__}")

    def bind_instance_method(self, operation_name: str, instance: Any):
        """
        绑定实例方法到具体实例

        Args:
            operation_name: 操作名称
            instance: 实例对象
        """
        if operation_name in self._instance_methods:
            func, _ = self._instance_methods[operation_name]
            # 更新实例方法记录
            self._instance_methods[operation_name] = (func, instance)
            logger.info(f"绑定实例方法: {operation_name} -> {instance.__class__.__name__}.{func.__name__}")

    def auto_bind_instance_methods(self, instance: Any):
        """
        自动绑定实例的所有已注册方法（保持向后兼容）

        Args:
            instance: 实例对象
        """
        self.register_instance(instance)

    def get_operation(self, operation_name: str) -> Callable:
        """获取操作函数"""
        return self._operations.get(operation_name)

    def list_operations(self) -> Dict[str, str]:
        """列出所有已注册的操作"""
        operations = {}
        # 添加已绑定的操作
        for name, func in self._operations.items():
            operations[name] = f"{func.__module__}.{func.__name__}"

        # 添加未绑定的实例方法
        for name, (func, instance) in self._instance_methods.items():
            if name not in operations:  # 避免重复
                status = "已绑定" if instance else "未绑定"
                operations[name] = f"{func.__module__}.{func.__name__} ({status})"

        return operations

    def has_operation(self, operation_name: str) -> bool:
        """检查操作是否存在"""
        return operation_name in self._operations or operation_name in self._instance_methods

    def get_unbound_methods(self) -> Dict[str, tuple]:
        """获取所有未绑定的实例方法"""
        return {
            name: (func, instance)
            for name, (func, instance) in self._instance_methods.items()
            if instance is None
        }


# 全局注册器实例
operation_registry = OperationRegistry()


def register_operation(operation_name: str):
    """
    注册操作的装饰器函数

    Usage:
        # 静态方法或函数
        @register_operation('user.login')
        def login(username, password):
            # 业务逻辑
            pass

        # 实例方法
        class UserBusiness:
            @register_operation('user.register')
            def register(self, username, password):
                # 业务逻辑
                pass

        # 创建实例后自动绑定
        user_business = UserBusiness()
        operation_registry.auto_bind_instance_methods(user_business)
    """
    return operation_registry.register(operation_name)
