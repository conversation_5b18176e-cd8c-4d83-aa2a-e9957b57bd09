"""
操作注册器 - 使用装饰器自动注册业务操作
支持实例方法的自动绑定
"""
import logging
import inspect
from functools import wraps
from typing import Dict, Callable, Any

logger = logging.getLogger(__name__)


class OperationRegistry:
    """操作注册器"""

    def __init__(self):
        self._operations: Dict[str, Callable] = {}
        self._instance_methods: Dict[str, tuple] = {}  # 存储实例方法信息

    def register(self, operation_name: str):
        """
        注册操作的装饰器
        支持实例方法和静态方法

        Args:
            operation_name: 操作名称，如 'user.login'
        """

        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                return func(*args, **kwargs)

            # 检查是否是实例方法（第一个参数是self）
            sig = inspect.signature(func)
            params = list(sig.parameters.keys())
            is_instance_method = len(params) > 0 and params[0] == 'self'

            if is_instance_method:
                # 对于实例方法，我们需要延迟绑定
                # 先存储方法信息，等实例创建后再绑定
                self._instance_methods[operation_name] = (func, None)
                logger.info(f"注册实例方法: {operation_name} -> {func.__module__}.{func.__name__} (待绑定)")
            else:
                # 对于静态方法或函数，直接注册
                self._operations[operation_name] = wrapper
                logger.info(f"注册操作: {operation_name} -> {func.__module__}.{func.__name__}")

            return wrapper

        return decorator

    def bind_instance_method(self, operation_name: str, instance: Any):
        """
        绑定实例方法到具体实例

        Args:
            operation_name: 操作名称
            instance: 实例对象
        """
        if operation_name in self._instance_methods:
            func, _ = self._instance_methods[operation_name]
            # 创建绑定方法
            bound_method = func.__get__(instance, type(instance))
            self._operations[operation_name] = bound_method
            # 更新实例方法记录
            self._instance_methods[operation_name] = (func, instance)
            logger.info(f"绑定实例方法: {operation_name} -> {instance.__class__.__name__}.{func.__name__}")

    def auto_bind_instance_methods(self, instance: Any):
        """
        自动绑定实例的所有已注册方法

        Args:
            instance: 实例对象
        """
        instance_class = type(instance)
        for operation_name, (func, bound_instance) in self._instance_methods.items():
            # 检查方法是否属于这个实例的类
            if hasattr(instance_class, func.__name__) and bound_instance is None:
                self.bind_instance_method(operation_name, instance)

    def get_operation(self, operation_name: str) -> Callable:
        """获取操作函数"""
        return self._operations.get(operation_name)

    def list_operations(self) -> Dict[str, str]:
        """列出所有已注册的操作"""
        operations = {}
        # 添加已绑定的操作
        for name, func in self._operations.items():
            operations[name] = f"{func.__module__}.{func.__name__}"

        # 添加未绑定的实例方法
        for name, (func, instance) in self._instance_methods.items():
            if name not in operations:  # 避免重复
                status = "已绑定" if instance else "未绑定"
                operations[name] = f"{func.__module__}.{func.__name__} ({status})"

        return operations

    def has_operation(self, operation_name: str) -> bool:
        """检查操作是否存在"""
        return operation_name in self._operations or operation_name in self._instance_methods

    def get_unbound_methods(self) -> Dict[str, tuple]:
        """获取所有未绑定的实例方法"""
        return {
            name: (func, instance)
            for name, (func, instance) in self._instance_methods.items()
            if instance is None
        }


# 全局注册器实例
operation_registry = OperationRegistry()


def register_operation(operation_name: str):
    """
    注册操作的装饰器函数

    Usage:
        # 静态方法或函数
        @register_operation('user.login')
        def login(username, password):
            # 业务逻辑
            pass

        # 实例方法
        class UserBusiness:
            @register_operation('user.register')
            def register(self, username, password):
                # 业务逻辑
                pass

        # 创建实例后自动绑定
        user_business = UserBusiness()
        operation_registry.auto_bind_instance_methods(user_business)
    """
    return operation_registry.register(operation_name)
