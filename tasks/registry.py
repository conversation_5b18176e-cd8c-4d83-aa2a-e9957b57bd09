"""
操作注册器 - 使用装饰器自动注册业务操作
"""
import logging
from functools import wraps
from typing import Dict, Callable

logger = logging.getLogger(__name__)


class OperationRegistry:
    """操作注册器"""

    def __init__(self):
        self._operations: Dict[str, Callable] = {}

    def register(self, operation_name: str):
        """
        注册操作的装饰器

        Args:
            operation_name: 操作名称，如 'user.login'
        """

        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                return func(*args, **kwargs)

            # 注册操作
            self._operations[operation_name] = wrapper
            logger.info(f"注册操作: {operation_name} -> {func.__module__}.{func.__name__}")
            return wrapper

        return decorator

    def get_operation(self, operation_name: str) -> Callable:
        """获取操作函数"""
        return self._operations.get(operation_name)

    def list_operations(self) -> Dict[str, str]:
        """列出所有已注册的操作"""
        return {
            name: f"{func.__module__}.{func.__name__}"
            for name, func in self._operations.items()
        }

    def has_operation(self, operation_name: str) -> bool:
        """检查操作是否存在"""
        return operation_name in self._operations


# 全局注册器实例
operation_registry = OperationRegistry()


def register_operation(operation_name: str):
    """
    注册操作的装饰器函数
    
    Usage:
        @register_operation('user.login')
        def login(username, password):
            # 业务逻辑
            pass
    """
    return operation_registry.register(operation_name)
