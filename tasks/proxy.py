"""
代理任务 - 参考remote_celery_worker的proxy模式
根据operation参数动态调用相应的业务函数
支持装饰器注册和手动注册两种方式
"""
import logging

from business.user_business import user_business
from celery_task.celery_config import app
from .registry import operation_registry

logger = logging.getLogger(__name__)

# 手动注册的操作映射表（向后兼容）
MANUAL_OPERATION_MAP = {
    # 用户相关操作
    'user.login': user_business.login,
    'user.register': user_business.register,
    'user.get_roles': user_business.get_user_roles,
    'user.get_permissions': user_business.get_user_permissions,
    'user.change_roles': user_business.change_user_roles,
    'user.check_permission': user_business.check_permission,
    'user.test1': user_business.test1_business,
}


def get_operation_function(operation):
    """
    获取操作函数，优先从装饰器注册中查找，然后从手动注册中查找

    Args:
        operation: 操作名称

    Returns:
        对应的业务函数，如果未找到返回None
    """
    # 1. 优先从装饰器注册中查找
    func = operation_registry.get_operation(operation)
    if func:
        return func

    # 2. 从手动注册中查找（向后兼容）
    return MANUAL_OPERATION_MAP.get(operation)


@app.task(name="yongzhou.proxy.execute")
def execute(operation, args=None, kwargs=None):
    """
    统一的代理任务执行器
    
    Args:
        operation: 操作路径，如 'user.login', 'user.register' 等
        args: 位置参数列表
        kwargs: 关键字参数字典
        
    Returns:
        任务执行结果
    """
    if args is None:
        args = []
    if kwargs is None:
        kwargs = {}

    logger.info(f"执行操作: {operation}, args: {args}, kwargs: {kwargs}")

    try:
        # 根据operation查找对应的业务函数
        business_func = get_operation_function(operation)

        if not business_func:
            return {
                'success': False,
                'message': f'未找到操作: {operation}',
                'operation': operation
            }

        # 调用业务函数
        result = business_func(*args, **kwargs)

        # logger.info(f"操作 {operation} 执行完成: {result}")
        return result

    except Exception as e:
        error_msg = f"执行操作 {operation} 时发生错误: {str(e)}"
        logger.error(error_msg)
        return {
            'success': False,
            'message': error_msg,
            'operation': operation
        }
