"""
Celery Worker启动脚本
"""
import os
import platform
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 初始化日志配置
from utils.logger import get_logger
from celery_task.celery_config import app
from settings.config import settings

logger = get_logger(__name__)

if __name__ == '__main__':
    logger.info(f"启动 {settings.app.name} Worker")
    logger.info(f"环境: {settings.app.environment}")
    logger.info(f"调试模式: {settings.app.debug}")

    # 启动flower监控
    import subprocess
    import threading


    def start_flower():
        env = os.environ.copy()
        env['FLOWER_UNAUTHENTICATED_API'] = 'true'
        subprocess.run(['celery', '-A', 'celery_task.celery_config', 'flower'], env=env)


    flower_thread = threading.Thread(target=start_flower, daemon=True)
    flower_thread.start()
    logger.info("Flower监控已启动")

    # 启动worker
    # 也可以使用命令: celery -A celery_task.celery_config worker --loglevel=info --pool=solo
    args = ['worker', f'--loglevel={settings.log.level.lower()}']
    if platform.system() == 'Windows':
        args.insert(1, '--pool=solo')
    app.worker_main(args)
