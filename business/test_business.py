from typing import Dict, Any

from models import Session
from tasks.registry import register_operation, operation_registry


class TestBusiness:
    """测试业务逻辑类"""

    def __init__(self):
        self.session = Session()

    @register_operation('test.get')
    def test(self, content: str) -> Dict[str, Any]:
        print(content)
        return {'success': True, 'message': 'test成功'}


# 创建业务实例并自动绑定实例方法
test_business = TestBusiness()
operation_registry.auto_bind_instance_methods(test_business)
