from typing import Dict, Any

from models import Session
from tasks.registry import register_operation


class TestBusiness:
    """患者业务逻辑类"""

    def __init__(self):
        self.session = Session()

    @register_operation('test.get')
    def test(self, content: str) -> Dict[str, Any]:
        print(content)
        return {'success': True, 'message': 'test成功'}


# 创建业务实例（重要：必须创建实例才能注册）
test_business = TestBusiness()
