from sqlalchemy import Column, Integer, String, Date, Boolean, TIMESTAMP, Text, Enum, ForeignKey, \
    text
from sqlalchemy.orm import relationship

from models.base_model import Base


class Patients(Base):
    """患者基础信息表"""
    __tablename__ = 'patients'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    patient_code = Column(String(30), unique=True, comment='患者编号')
    name = Column(String(50), nullable=False, comment='患者姓名')
    gender = Column(Enum('男', '女'), comment='性别')
    birth_date = Column(Date, nullable=False, comment='出生日期')
    age = Column(Integer, nullable=False, comment='年龄')
    id_card = Column(String(18), unique=True, nullable=False, comment='身份证号')
    phone = Column(String(20), comment='联系电话')
    region_id = Column(Integer, ForeignKey('regions.id'), comment='地区ID')
    address = Column(Text, comment='详细地址')
    emergency_contact = Column(String(50), comment='紧急联系人')
    emergency_phone = Column(String(20), comment='紧急联系人电话')
    occupation = Column(String(100), comment='职业')
    education = Column(Enum('小学', '初中', '高中', '大专', '本科', '研究生', '其他'),
                       comment='教育程度')
    marital_status = Column(Enum('未婚', '已婚', '离异', '丧偶'), comment='婚姻状况')
    insurance_type = Column(Enum('城镇职工', '城乡居民', '新农合', '商业保险', '自费', '其他'),
                            comment='医保类型')
    is_active = Column(Boolean, default=True, comment='是否有效')
    created_time = Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), comment='创建时间')
    updated_time = Column(TIMESTAMP,
                          server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
                          comment='更新时间')

    # 关联关系
    region = relationship("Regions", backref="patients")
    chronic_diseases = relationship("PatientChronicDiseases", back_populates="patient")
    diagnosis_records = relationship("DiagnosisRecords", back_populates="patient")
    treat_details = relationship("TreatDetail", back_populates="patient")

    def __repr__(self):
        return f"<Patients(id={self.id}, name='{self.name}', id_card='{self.id_card}')>"
