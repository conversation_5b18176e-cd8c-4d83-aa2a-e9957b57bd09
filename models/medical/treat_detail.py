from sqlalchemy import Column, Integer, TIMESTAMP, String, Text, ForeignKey, text
from sqlalchemy.orm import relationship

from models.base_model import Base


class TreatDetail(Base):
    """就诊详情"""
    __tablename__ = 'treat_detail'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    patient_id = Column(Integer, ForeignKey('patients.id'), nullable=False, comment='患者ID')
    treat_status = Column(Text, comment='症状描述')
    management_status_id = Column(Integer, ForeignKey('management_status.id'), comment='就诊状态ID')
    treat_plan = Column(Text, comment='治疗方案')
    treat_doctor = Column(String(50), nullable=False, comment='医生')
    treat_dept = Column(String(50), comment='就诊科室')
    treat_time = Column(TIMESTAMP, comment='就诊日期')
    created_time = Column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), comment='创建时间')

    # 关联关系
    patient = relationship("Patients", back_populates="treat_details")
    management_status = relationship("ManagementStatus", backref="treat_details")

    def __repr__(self):
        return (f"<TreatDetail(id={self.id}, patient_id={self.patient_id}, treat_doctor='"
                f"{self.treat_doctor}')>")
